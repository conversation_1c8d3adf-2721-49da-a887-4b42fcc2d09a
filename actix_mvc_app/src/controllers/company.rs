use crate::controllers::error::render_company_not_found;
use crate::db::company::*;
use crate::db::document::*;
use crate::models::document::DocumentType;
use crate::utils::render_template;
use actix_web::HttpRequest;
use actix_web::{HttpResponse, Result, web};

use heromodels::models::biz::{BusinessType, CompanyStatus};
use serde::Deserialize;
use std::fs;
use tera::{Context, Tera};

// Form structs for company operations
#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct CompanyRegistrationForm {
    pub company_name: String,
    pub company_type: String,
    pub shareholders: String,
    pub company_purpose: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CompanyEditForm {
    pub company_name: String,
    pub company_type: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub website: Option<String>,
    pub address: Option<String>,
    pub industry: Option<String>,
    pub description: Option<String>,
    pub fiscal_year_end: Option<String>,
    pub status: String,
}

pub struct CompanyController;

impl CompanyController {
    // Display the company management dashboard
    pub async fn index(tmpl: web::Data<Tera>, req: HttpRequest) -> Result<HttpResponse> {
        let mut context = Context::new();

        // Add active_page for navigation highlighting
        context.insert("active_page", &"company");

        // Load companies from database
        let companies = match get_companies() {
            Ok(companies) => companies,
            Err(e) => {
                log::error!("Failed to get companies from database: {}", e);
                vec![]
            }
        };

        context.insert("companies", &companies);

        // Parse query parameters
        let query_string = req.query_string();

        // Check for success message
        if let Some(pos) = query_string.find("success=") {
            let start = pos + 8; // length of "success="
            let end = query_string[start..]
                .find('&')
                .map_or(query_string.len(), |e| e + start);
            let success = &query_string[start..end];
            let decoded = urlencoding::decode(success).unwrap_or_else(|_| success.into());
            context.insert("success", &decoded);
        }

        // Check for entity context
        if let Some(pos) = query_string.find("entity=") {
            let start = pos + 7; // length of "entity="
            let end = query_string[start..]
                .find('&')
                .map_or(query_string.len(), |e| e + start);
            let entity = &query_string[start..end];
            context.insert("entity", &entity);

            // Also get entity name if present
            if let Some(pos) = query_string.find("entity_name=") {
                let start = pos + 12; // length of "entity_name="
                let end = query_string[start..]
                    .find('&')
                    .map_or(query_string.len(), |e| e + start);
                let entity_name = &query_string[start..end];
                let decoded_name =
                    urlencoding::decode(entity_name).unwrap_or_else(|_| entity_name.into());
                context.insert("entity_name", &decoded_name);
            }
        }

        render_template(&tmpl, "company/index.html", &context)
    }

    // Display company edit form
    pub async fn edit_form(
        tmpl: web::Data<Tera>,
        path: web::Path<String>,
        req: HttpRequest,
    ) -> Result<HttpResponse> {
        let company_id_str = path.into_inner();
        let mut context = Context::new();

        // Add active_page for navigation highlighting
        context.insert("active_page", &"company");

        // Parse query parameters for success/error messages
        let query_string = req.query_string();

        // Check for success message
        if let Some(pos) = query_string.find("success=") {
            let start = pos + 8; // length of "success="
            let end = query_string[start..]
                .find('&')
                .map_or(query_string.len(), |e| e + start);
            let success = &query_string[start..end];
            let decoded = urlencoding::decode(success).unwrap_or_else(|_| success.into());
            context.insert("success", &decoded);
        }

        // Check for error message
        if let Some(pos) = query_string.find("error=") {
            let start = pos + 6; // length of "error="
            let end = query_string[start..]
                .find('&')
                .map_or(query_string.len(), |e| e + start);
            let error = &query_string[start..end];
            let decoded = urlencoding::decode(error).unwrap_or_else(|_| error.into());
            context.insert("error", &decoded);
        }

        // Parse company ID
        let company_id = match company_id_str.parse::<u32>() {
            Ok(id) => id,
            Err(_) => return render_company_not_found(&tmpl, Some(&company_id_str)).await,
        };

        // Fetch company from database
        if let Ok(Some(company)) = get_company_by_id(company_id) {
            context.insert("company", &company);

            // Format timestamps for display
            let incorporation_date =
                chrono::DateTime::from_timestamp(company.incorporation_date, 0)
                    .map(|dt| dt.format("%Y-%m-%d").to_string())
                    .unwrap_or_else(|| "Unknown".to_string());
            context.insert("incorporation_date_formatted", &incorporation_date);

            render_template(&tmpl, "company/edit.html", &context)
        } else {
            render_company_not_found(&tmpl, Some(&company_id_str)).await
        }
    }

    // View company details
    pub async fn view_company(
        tmpl: web::Data<Tera>,
        path: web::Path<String>,
        req: HttpRequest,
    ) -> Result<HttpResponse> {
        let company_id_str = path.into_inner();
        let mut context = Context::new();

        // Add active_page for navigation highlighting
        context.insert("active_page", &"company");
        context.insert("company_id", &company_id_str);

        // Parse query parameters for success/error messages
        let query_string = req.query_string();

        // Check for success message
        if let Some(pos) = query_string.find("success=") {
            let start = pos + 8; // length of "success="
            let end = query_string[start..]
                .find('&')
                .map_or(query_string.len(), |e| e + start);
            let success = &query_string[start..end];
            let decoded = urlencoding::decode(success).unwrap_or_else(|_| success.into());
            context.insert("success", &decoded);
        }

        // Check for error message
        if let Some(pos) = query_string.find("error=") {
            let start = pos + 6; // length of "error="
            let end = query_string[start..]
                .find('&')
                .map_or(query_string.len(), |e| e + start);
            let error = &query_string[start..end];
            let decoded = urlencoding::decode(error).unwrap_or_else(|_| error.into());
            context.insert("error", &decoded);
        }

        // Parse company ID
        let company_id = match company_id_str.parse::<u32>() {
            Ok(id) => id,
            Err(_) => return render_company_not_found(&tmpl, Some(&company_id_str)).await,
        };

        // Fetch company from database
        if let Ok(Some(company)) = get_company_by_id(company_id) {
            context.insert("company", &company);

            // Format timestamps for display
            let incorporation_date =
                chrono::DateTime::from_timestamp(company.incorporation_date, 0)
                    .map(|dt| dt.format("%Y-%m-%d").to_string())
                    .unwrap_or_else(|| "Unknown".to_string());
            context.insert("incorporation_date_formatted", &incorporation_date);

            // Get shareholders for this company
            let shareholders = match get_company_shareholders(company_id) {
                Ok(shareholders) => shareholders,
                Err(e) => {
                    log::error!(
                        "Failed to get shareholders for company {}: {}",
                        company_id,
                        e
                    );
                    vec![]
                }
            };
            context.insert("shareholders", &shareholders);

            render_template(&tmpl, "company/view.html", &context)
        } else {
            render_company_not_found(&tmpl, Some(&company_id_str)).await
        }
    }

    // Switch to entity context
    pub async fn switch_entity(path: web::Path<String>) -> Result<HttpResponse> {
        let company_id_str = path.into_inner();

        // Parse company ID
        let company_id = match company_id_str.parse::<u32>() {
            Ok(id) => id,
            Err(_) => {
                return Ok(HttpResponse::Found()
                    .append_header(("Location", "/company"))
                    .finish());
            }
        };

        // Get company from database
        let company_name = match get_company_by_id(company_id) {
            Ok(Some(company)) => company.name,
            Ok(None) => {
                return Ok(HttpResponse::Found()
                    .append_header(("Location", "/company"))
                    .finish());
            }
            Err(e) => {
                log::error!("Failed to get company for switch: {}", e);
                return Ok(HttpResponse::Found()
                    .append_header(("Location", "/company"))
                    .finish());
            }
        };

        // In a real application, we would set a session/cookie for the current entity
        // Here we'll redirect back to the company page with a success message and entity parameter
        let success_message = format!("Switched to {} entity context", company_name);
        let encoded_message = urlencoding::encode(&success_message);

        Ok(HttpResponse::Found()
            .append_header((
                "Location",
                format!(
                    "/company?success={}&entity={}&entity_name={}",
                    encoded_message,
                    company_id_str,
                    urlencoding::encode(&company_name)
                ),
            ))
            .finish())
    }

    // Process company registration
    pub async fn register(mut form: actix_multipart::Multipart) -> Result<HttpResponse> {
        use actix_web::http::header;
        use chrono::Utc;
        use futures_util::stream::StreamExt as _;
        use std::collections::HashMap;

        let mut fields: HashMap<String, String> = HashMap::new();
        let mut uploaded_files = Vec::new();

        // Parse multipart form
        while let Some(Ok(mut field)) = form.next().await {
            let content_disposition = field.content_disposition();
            let field_name = content_disposition
                .get_name()
                .unwrap_or("unknown")
                .to_string();
            let filename = content_disposition.get_filename().map(|f| f.to_string());

            if field_name.starts_with("contract-") || field_name.ends_with("-doc") {
                // Handle file upload
                if let Some(filename) = filename {
                    let mut file_data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let data = chunk.unwrap();
                        file_data.extend_from_slice(&data);
                    }

                    if !file_data.is_empty() {
                        uploaded_files.push((field_name, filename, file_data));
                    }
                }
            } else {
                // Handle form field
                let mut value = Vec::new();
                while let Some(chunk) = field.next().await {
                    let data = chunk.unwrap();
                    value.extend_from_slice(&data);
                }

                fields.insert(field_name, String::from_utf8_lossy(&value).to_string());
            }
        }

        // Extract company details
        let company_name = fields.get("company_name").cloned().unwrap_or_default();
        let company_type_str = fields.get("company_type").cloned().unwrap_or_default();
        let company_purpose = fields.get("company_purpose").cloned().unwrap_or_default();
        let shareholders_str = fields.get("shareholders").cloned().unwrap_or_default();

        // Extract new contact fields
        let company_email = fields.get("company_email").cloned().unwrap_or_default();
        let company_phone = fields.get("company_phone").cloned().unwrap_or_default();
        let company_website = fields.get("company_website").cloned().unwrap_or_default();
        let company_address = fields.get("company_address").cloned().unwrap_or_default();
        let company_industry = fields.get("company_industry").cloned().unwrap_or_default();
        let fiscal_year_end = fields.get("fiscal_year_end").cloned().unwrap_or_default();

        // Validate required fields
        if company_name.is_empty() || company_type_str.is_empty() {
            return Ok(HttpResponse::SeeOther()
                .append_header((
                    header::LOCATION,
                    "/company?error=Company name and type are required",
                ))
                .finish());
        }

        if company_email.trim().is_empty() {
            return Ok(HttpResponse::SeeOther()
                .append_header((header::LOCATION, "/company?error=Company email is required"))
                .finish());
        }

        if company_phone.trim().is_empty() {
            return Ok(HttpResponse::SeeOther()
                .append_header((header::LOCATION, "/company?error=Company phone is required"))
                .finish());
        }

        if company_address.trim().is_empty() {
            return Ok(HttpResponse::SeeOther()
                .append_header((
                    header::LOCATION,
                    "/company?error=Company address is required",
                ))
                .finish());
        }

        // Parse business type
        let business_type = match company_type_str.as_str() {
            "Startup FZC" => BusinessType::Starter,
            "Growth FZC" => BusinessType::Global,
            "Cooperative FZC" => BusinessType::Coop,
            "Single FZC" => BusinessType::Single,
            "Twin FZC" => BusinessType::Twin,
            _ => BusinessType::Single, // Default
        };

        // Generate registration number (in real app, this would be more sophisticated)
        let registration_number = format!(
            "FZC-{}-{}",
            Utc::now().format("%Y%m%d"),
            company_name
                .chars()
                .take(3)
                .collect::<String>()
                .to_uppercase()
        );

        // Create company in database
        match create_new_company(
            company_name.clone(),
            registration_number,
            Utc::now().timestamp(),
            business_type,
            company_email,
            company_phone,
            company_website,
            company_address,
            company_industry,
            company_purpose,
            fiscal_year_end,
        ) {
            Ok((company_id, _company)) => {
                // TODO: Parse and create shareholders if provided
                if !shareholders_str.is_empty() {
                    // For now, just log the shareholders - in a real app, parse and create them
                    log::info!(
                        "Shareholders for company {}: {}",
                        company_id,
                        shareholders_str
                    );
                }

                // Save uploaded documents
                if !uploaded_files.is_empty() {
                    log::info!(
                        "Processing {} uploaded files for company {}",
                        uploaded_files.len(),
                        company_id
                    );

                    // Create uploads directory if it doesn't exist
                    let upload_dir = format!("/tmp/company_{}_documents", company_id);
                    if let Err(e) = fs::create_dir_all(&upload_dir) {
                        log::error!("Failed to create upload directory: {}", e);
                    } else {
                        // Save each uploaded file
                        for (field_name, filename, file_data) in uploaded_files {
                            // Determine document type based on field name
                            let doc_type = match field_name.as_str() {
                                name if name.contains("shareholder") => DocumentType::Articles,
                                name if name.contains("bank") => DocumentType::Financial,
                                name if name.contains("cooperative") => DocumentType::Articles,
                                name if name.contains("digital") => DocumentType::Legal,
                                name if name.contains("contract") => DocumentType::Contract,
                                _ => DocumentType::Other,
                            };

                            // Generate unique filename
                            let timestamp = Utc::now().timestamp();
                            let file_extension = filename.split('.').last().unwrap_or("pdf");
                            let unique_filename = format!(
                                "{}_{}.{}",
                                timestamp,
                                filename.replace(" ", "_"),
                                file_extension
                            );
                            let file_path = format!("{}/{}", upload_dir, unique_filename);

                            // Save file to disk
                            if let Err(e) = fs::write(&file_path, &file_data) {
                                log::error!("Failed to save file {}: {}", filename, e);
                                continue;
                            }

                            // Save document metadata to database
                            let file_size = file_data.len() as u64;
                            let mime_type = match file_extension {
                                "pdf" => "application/pdf",
                                "doc" | "docx" => "application/msword",
                                "jpg" | "jpeg" => "image/jpeg",
                                "png" => "image/png",
                                _ => "application/octet-stream",
                            }
                            .to_string();

                            match create_new_document(
                                filename.clone(),
                                file_path,
                                file_size,
                                mime_type,
                                company_id,
                                "System".to_string(), // uploaded_by
                                doc_type,
                                Some("Uploaded during company registration".to_string()),
                                false, // not public by default
                                None,  // checksum
                            ) {
                                Ok(_) => {
                                    log::info!("Successfully saved document: {}", filename);
                                }
                                Err(e) => {
                                    log::error!(
                                        "Failed to save document metadata for {}: {}",
                                        filename,
                                        e
                                    );
                                }
                            }
                        }
                    }
                }

                let success_message = format!(
                    "Successfully registered {} as a {}",
                    company_name, company_type_str
                );

                Ok(HttpResponse::SeeOther()
                    .append_header((
                        header::LOCATION,
                        format!("/company?success={}", urlencoding::encode(&success_message)),
                    ))
                    .finish())
            }
            Err(e) => {
                log::error!("Failed to create company: {}", e);
                Ok(HttpResponse::SeeOther()
                    .append_header((
                        header::LOCATION,
                        "/company?error=Failed to register company",
                    ))
                    .finish())
            }
        }
    }

    // Process company edit form
    pub async fn edit(
        tmpl: web::Data<Tera>,
        path: web::Path<String>,
        form: web::Form<CompanyEditForm>,
    ) -> Result<HttpResponse> {
        use actix_web::http::header;

        let company_id_str = path.into_inner();

        // Parse company ID
        let company_id = match company_id_str.parse::<u32>() {
            Ok(id) => id,
            Err(_) => return render_company_not_found(&tmpl, Some(&company_id_str)).await,
        };

        // Validate required fields
        if form.company_name.trim().is_empty() {
            return Ok(HttpResponse::SeeOther()
                .append_header((
                    header::LOCATION,
                    format!(
                        "/company/edit/{}?error=Company name is required",
                        company_id
                    ),
                ))
                .finish());
        }

        // Parse business type
        let business_type = match form.company_type.as_str() {
            "Startup FZC" => BusinessType::Starter,
            "Growth FZC" => BusinessType::Global,
            "Cooperative FZC" => BusinessType::Coop,
            "Single FZC" => BusinessType::Single,
            "Twin FZC" => BusinessType::Twin,
            _ => BusinessType::Single, // Default
        };

        // Parse status
        let status = match form.status.as_str() {
            "Active" => CompanyStatus::Active,
            "Inactive" => CompanyStatus::Inactive,
            "Suspended" => CompanyStatus::Suspended,
            _ => CompanyStatus::Active, // Default
        };

        // Update company in database
        match update_company(
            company_id,
            Some(form.company_name.clone()),
            form.email.clone(),
            form.phone.clone(),
            form.website.clone(),
            form.address.clone(),
            form.industry.clone(),
            form.description.clone(),
            form.fiscal_year_end.clone(),
            Some(status),
            Some(business_type),
        ) {
            Ok(_) => {
                let success_message = format!("Successfully updated {}", form.company_name);
                Ok(HttpResponse::SeeOther()
                    .append_header((
                        header::LOCATION,
                        format!(
                            "/company/view/{}?success={}",
                            company_id,
                            urlencoding::encode(&success_message)
                        ),
                    ))
                    .finish())
            }
            Err(e) => {
                log::error!("Failed to update company {}: {}", company_id, e);
                Ok(HttpResponse::SeeOther()
                    .append_header((
                        header::LOCATION,
                        format!(
                            "/company/edit/{}?error=Failed to update company",
                            company_id
                        ),
                    ))
                    .finish())
            }
        }
    }
}
