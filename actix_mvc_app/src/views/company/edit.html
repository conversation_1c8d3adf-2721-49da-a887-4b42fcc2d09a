{% extends "base.html" %}

{% block title %}Edit {{ company.name }} - Company Management{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="bi bi-pencil-square me-2"></i>Edit Company</h2>
        <div>
            <a href="/company/view/{{ company.base_data.id }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i>Back to Company
            </a>
            <a href="/company" class="btn btn-outline-secondary">
                <i class="bi bi-building me-1"></i>All Companies
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    {% if success %}
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-2"></i>{{ success }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if error %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>{{ error }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Edit Form -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-building me-2"></i>Company Information</h5>
        </div>
        <div class="card-body">
            <form action="/company/edit/{{ company.base_data.id }}" method="post" id="editCompanyForm">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Basic Information</h6>

                        <div class="mb-3">
                            <label for="company_name" class="form-label">Company Name <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="company_name" name="company_name"
                                value="{{ company.name }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="company_type" class="form-label">Company Type <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="company_type" name="company_type" required>
                                <option value="Startup FZC" {% if company.business_type=="Starter" %}selected{% endif
                                    %}>Startup FZC</option>
                                <option value="Growth FZC" {% if company.business_type=="Global" %}selected{% endif %}>
                                    Growth FZC</option>
                                <option value="Cooperative FZC" {% if company.business_type=="Coop" %}selected{% endif
                                    %}>Cooperative FZC</option>
                                <option value="Single FZC" {% if company.business_type=="Single" %}selected{% endif %}>
                                    Single FZC</option>
                                <option value="Twin FZC" {% if company.business_type=="Twin" %}selected{% endif %}>Twin
                                    FZC</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="Active" {% if company.status=="Active" %}selected{% endif %}>Active
                                </option>
                                <option value="Inactive" {% if company.status=="Inactive" %}selected{% endif %}>Inactive
                                </option>
                                <option value="Suspended" {% if company.status=="Suspended" %}selected{% endif %}>
                                    Suspended</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="industry" class="form-label">Industry</label>
                            <input type="text" class="form-control" id="industry" name="industry"
                                value="{{ company.industry | default(value='') }}">
                        </div>

                        <div class="mb-3">
                            <label for="fiscal_year_end" class="form-label">Fiscal Year End</label>
                            <input type="text" class="form-control" id="fiscal_year_end" name="fiscal_year_end"
                                value="{{ company.fiscal_year_end | default(value='') }}"
                                placeholder="MM-DD (e.g., 12-31)" pattern="^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$"
                                title="Enter date in MM-DD format (e.g., 12-31)">
                            <div class="form-text">Enter the last day of your company's fiscal year (MM-DD format)</div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Contact Information</h6>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email"
                                value="{{ company.email | default(value='') }}">
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                value="{{ company.phone | default(value='') }}">
                        </div>

                        <div class="mb-3">
                            <label for="website" class="form-label">Website</label>
                            <input type="url" class="form-control" id="website" name="website"
                                value="{{ company.website | default(value='') }}" placeholder="https://example.com">
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address"
                                rows="3">{{ company.address | default(value='') }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">Additional Information</h6>

                        <div class="mb-3">
                            <label for="description" class="form-label">Company Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                placeholder="Describe the company's purpose and activities">{{ company.description | default(value='') }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Read-only Information -->
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">Registration Information (Read-only)</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Registration Number</label>
                                    <input type="text" class="form-control" value="{{ company.registration_number }}"
                                        readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Incorporation Date</label>
                                    <input type="text" class="form-control" value="{{ incorporation_date_formatted }}"
                                        readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Company ID</label>
                                    <input type="text" class="form-control" value="{{ company.base_data.id }}" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <div>
                        <a href="/company/view/{{ company.base_data.id }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                    </div>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Update Company
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Form validation
        const form = document.getElementById('editCompanyForm');
        const companyName = document.getElementById('company_name');

        form.addEventListener('submit', function (e) {
            if (companyName.value.trim() === '') {
                e.preventDefault();
                showValidationAlert('Company name is required', companyName);
            }
        });

        // Function to show validation alert with consistent styling
        function showValidationAlert(message, focusElement) {
            // Remove existing alerts
            const existingAlerts = document.querySelectorAll('.validation-alert');
            existingAlerts.forEach(alert => alert.remove());

            // Create new alert
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning alert-dismissible fade show validation-alert mt-3';
            alertDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span>${message}</span>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Insert alert at the top of the form
            const form = document.getElementById('editCompanyForm');
            form.insertBefore(alertDiv, form.firstChild);

            // Focus on the problematic field
            if (focusElement) {
                focusElement.focus();
                focusElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Auto-format website URL
        const websiteInput = document.getElementById('website');
        websiteInput.addEventListener('blur', function () {
            let value = this.value.trim();
            if (value && !value.startsWith('http://') && !value.startsWith('https://')) {
                this.value = 'https://' + value;
            }
        });
    });
</script>
{% endblock %}