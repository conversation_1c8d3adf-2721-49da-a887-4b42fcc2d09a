<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <i class="bi bi-file-earmark-plus me-1"></i> Register a New Company / Legal Entity
    </div>
    <div class="card-body">
        <form id="companyRegistrationForm" action="/company/register" method="post" enctype="multipart/form-data">
            <!-- Progress bar -->
            <div class="progress mb-4">
                <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25"
                    aria-valuemin="0" aria-valuemax="100">Step 1 of 4</div>
            </div>

            <!-- Step indicators -->
            <div class="d-flex justify-content-between mb-4">
                <div class="step-indicator active" id="step-indicator-1">
                    <span class="badge rounded-pill bg-success">1</span> General Info
                </div>
                <div class="step-indicator" id="step-indicator-2">
                    <span class="badge rounded-pill bg-secondary">2</span> Company Type
                </div>
                <div class="step-indicator" id="step-indicator-3">
                    <span class="badge rounded-pill bg-secondary">3</span> Shareholders
                </div>
                <div class="step-indicator" id="step-indicator-4">
                    <span class="badge rounded-pill bg-secondary">4</span> Documents
                </div>
            </div>

            <!-- Step 1: General Information -->
            <div class="form-step" id="step-1">
                <h4 class="mb-3">General Company Information</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="companyName" class="form-label">Company Name <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="companyName" name="company_name" required>
                            <div class="form-text">Choose a unique name for your company or entity.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="companyEmail" class="form-label">Company Email <span
                                    class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="companyEmail" name="company_email" required>
                            <div class="form-text">Primary contact email for your company.</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="companyPhone" class="form-label">Company Phone <span
                                    class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="companyPhone" name="company_phone" required>
                            <div class="form-text">Primary contact phone number.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="companyWebsite" class="form-label">Website</label>
                            <input type="url" class="form-control" id="companyWebsite" name="company_website"
                                placeholder="https://example.com">
                            <div class="form-text">Company website (optional).</div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="companyAddress" class="form-label">Business Address <span
                            class="text-danger">*</span></label>
                    <textarea class="form-control" id="companyAddress" name="company_address" rows="2" required
                        placeholder="Enter your business address"></textarea>
                    <div class="form-text">Physical business address for official correspondence.</div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="companyIndustry" class="form-label">Industry</label>
                            <select class="form-select" id="companyIndustry" name="company_industry">
                                <option value="">Select industry (optional)</option>
                                <option value="Technology">Technology</option>
                                <option value="Finance">Finance</option>
                                <option value="Healthcare">Healthcare</option>
                                <option value="Education">Education</option>
                                <option value="Retail">Retail</option>
                                <option value="Manufacturing">Manufacturing</option>
                                <option value="Real Estate">Real Estate</option>
                                <option value="Consulting">Consulting</option>
                                <option value="Media">Media</option>
                                <option value="Transportation">Transportation</option>
                                <option value="Energy">Energy</option>
                                <option value="Agriculture">Agriculture</option>
                                <option value="Other">Other</option>
                            </select>
                            <div class="form-text">Select your primary business industry.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="fiscalYearEnd" class="form-label">Fiscal Year End</label>
                            <input type="text" class="form-control" id="fiscalYearEnd" name="fiscal_year_end"
                                placeholder="MM-DD (e.g., 12-31)" pattern="^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$"
                                title="Enter date in MM-DD format">
                            <div class="form-text">Last day of your fiscal year (optional).</div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="companyPurpose" class="form-label">Purpose / Description</label>
                    <textarea class="form-control" id="companyPurpose" name="company_purpose" rows="3"
                        placeholder="Describe your business activities, goals, and any special requirements."></textarea>
                </div>
                <div class="mb-3">
                    <label for="registrationDate" class="form-label">Desired Registration Date</label>
                    <input type="date" class="form-control" id="registrationDate" name="registration_date">
                </div>
                <div class="d-flex justify-content-end mt-4">
                    <button type="button" class="btn btn-success" onclick="nextStep(1)">Next <i
                            class="bi bi-arrow-right"></i></button>
                </div>
            </div>

            <!-- Step 2: Company Type -->
            <div class="form-step" id="step-2" style="display: none;">
                <h4 class="mb-3">Select Company Type</h4>
                <div class="mb-3">
                    <label for="companyType" class="form-label">Company Type</label>
                    <select class="form-select" id="companyType" name="company_type" required
                        onchange="updateCompanyTypeInfo()">
                        <option value="Single FZC">Single FZC (1 shareholder) - $20/month</option>
                        <option value="Startup FZC">Startup FZC (up to 5 shareholders) - $50/month</option>
                        <option value="Growth FZC">Growth FZC (up to 20 shareholders) - $100/month</option>
                        <option value="Global FZC">Global FZC (unlimited shareholders) - $200/month + per shareholder
                        </option>
                        <option value="Cooperative FZC">Cooperative FZC (unlimited members, democratic) - $200/month +
                            per member</option>
                    </select>
                    <div class="form-text" id="companyTypeHelp">
                        Select the company type to see capabilities and requirements.
                    </div>
                </div>
                <div class="alert alert-info" id="pricingInfo">
                    <h6><i class="bi bi-info-circle me-2"></i>Pricing Information</h6>
                    <p class="mb-1">Setup fee: <span id="setupFee">$20</span> (one-time)</p>
                    <p class="mb-1">Monthly fee: <span id="monthlyFee">$20</span></p>
                    <p class="mb-1">ZDFZ Twin fee: $2/month (included)</p>
                    <p class="mb-0">First year total: <span id="totalFirstYear">$264</span> <small>(or <span
                                id="discountedYearly">$211.20</span> with annual payment)</small></p>
                    <div class="mt-2">
                        <small><i class="bi bi-tag me-1"></i>Save 20% with annual payment, 40% with 2-year
                            payment</small>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header bg-light">Capabilities</div>
                    <div class="card-body">
                        <table class="table table-hover" id="capabilitiesTable">
                            <thead>
                                <tr>
                                    <th>Capability</th>
                                    <th class="text-center">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr id="cap-digital-assets">
                                    <td>Hold Digital Assets (Blockchain)</td>
                                    <td class="text-center"><i class="bi bi-check-circle-fill text-success"></i></td>
                                </tr>
                                <tr id="cap-ecosystem">
                                    <td>Participate in Freezone Ecosystem</td>
                                    <td class="text-center"><i class="bi bi-check-circle-fill text-success"></i></td>
                                </tr>
                                <tr id="cap-ai-dispute">
                                    <td>AI Dispute Resolution</td>
                                    <td class="text-center"><i class="bi bi-check-circle-fill text-success"></i></td>
                                </tr>
                                <tr id="cap-digital-signing">
                                    <td>Digital Signing / Smart Contracts</td>
                                    <td class="text-center"><i class="bi bi-check-circle-fill text-success"></i></td>
                                </tr>
                                <tr id="cap-issue-assets">
                                    <td>Issue Digital Assets</td>
                                    <td class="text-center"><i class="bi bi-x-circle-fill text-danger"></i></td>
                                </tr>
                                <tr id="cap-external-shares">
                                    <td>Hold Shares in External Company</td>
                                    <td class="text-center"><i class="bi bi-check-circle-fill text-success"></i></td>
                                </tr>
                                <tr id="cap-bank-account">
                                    <td>Connect to Bank Account</td>
                                    <td class="text-center"><i class="bi bi-check-circle-fill text-success"></i></td>
                                </tr>
                                <tr id="cap-physical-assets">
                                    <td>Hold External Physical Assets</td>
                                    <td class="text-center"><i class="bi bi-x-circle-fill text-danger"></i></td>
                                </tr>
                                <tr id="cap-democratic">
                                    <td>Democratic Governance</td>
                                    <td class="text-center"><i class="bi bi-x-circle-fill text-danger"></i></td>
                                </tr>
                                <tr id="cap-collective">
                                    <td>Digital Collective Decision-Making</td>
                                    <td class="text-center"><i class="bi bi-x-circle-fill text-danger"></i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="table-responsive mb-3">
                    <table class="table table-bordered table-sm align-middle text-center">
                        <thead class="table-light">
                            <tr>
                                <th>Type</th>
                                <th>Max Shareholders</th>
                                <th>Issue Digital Assets</th>
                                <th>External Shares</th>
                                <th>Bank Account</th>
                                <th>Physical Assets</th>
                                <th>Democratic</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Single FZC</td>
                                <td>1</td>
                                <td>-</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>Startup FZC</td>
                                <td>5</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>Growth FZC</td>
                                <td>20</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>Global FZC</td>
                                <td>Unlimited</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>Cooperative FZC</td>
                                <td>Unlimited</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>✔</td>
                                <td>✔</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)"><i
                            class="bi bi-arrow-left"></i> Previous</button>
                    <button type="button" class="btn btn-success" onclick="nextStep(2)">Next <i
                            class="bi bi-arrow-right"></i></button>
                </div>
            </div>

            <!-- Step 3: Shareholders -->
            <div class="form-step" id="step-3" style="display: none;">
                <h4 class="mb-3">Shareholders / Members</h4>
                <div class="mb-3">
                    <label for="shareholderStructure" class="form-label">Shareholder Structure</label>
                    <select class="form-select" id="shareholderStructure" name="shareholder_structure"
                        onchange="updateShareholderPercentages()">
                        <option value="equal">Equal distribution</option>
                        <option value="custom">Custom percentages</option>
                    </select>
                    <div class="form-text">Select how ownership is distributed among shareholders.</div>
                </div>

                <div class="mb-3" id="shareholderSection">
                    <label class="form-label">Shareholders / Members</label>
                    <div id="shareholdersContainer">
                        <div class="input-group mb-2 shareholder-input">
                            <input type="text" class="form-control shareholder-name" name="shareholder[]"
                                placeholder="Shareholder name" required>
                            <span class="input-group-text">%</span>
                            <input type="number" class="form-control shareholder-percentage" name="percentage[]" min="0"
                                max="100" step="0.01" value="100" readonly>
                            <button type="button" class="btn btn-outline-success add-shareholder"
                                title="Add another shareholder">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-text" id="shareholderHelp">Add shareholders or members. Max depends on company
                        type.</div>
                    <div class="form-text text-danger" id="percentageWarning" style="display: none;">Total percentage
                        must equal 100%.</div>
                    <input type="hidden" id="shareholders" name="shareholders" value="">
                    <input type="hidden" id="shareholderPercentages" name="shareholder_percentages" value="">
                </div>
                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(3)"><i
                            class="bi bi-arrow-left"></i> Previous</button>
                    <button type="button" class="btn btn-success" onclick="nextStep(3)">Next <i
                            class="bi bi-arrow-right"></i></button>
                </div>
            </div>

            <!-- Step 4: Supporting Documents -->
            <div class="form-step" id="step-4" style="display: none;">
                <h4 class="mb-3">Required Documents & Contracts</h4>

                <!-- Required Documents Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-file-earmark me-2"></i>Required Documents</h5>
                    </div>
                    <div class="card-body" id="requiredDocumentsContainer">
                        <!-- Common documents for all company types -->
                        <p class="card-text">Please upload the following required documents:</p>

                        <table class="table table-striped table-hover align-middle">
                            <thead>
                                <tr>
                                    <th style="width: 5%"><i class="bi bi-file-earmark"></i></th>
                                    <th style="width: 20%">Document</th>
                                    <th style="width: 45%">Description</th>
                                    <th style="width: 15%">Status</th>
                                    <th style="width: 15%">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><i class="bi bi-file-earmark-text text-primary"></i></td>
                                    <td>ID Proof for Principal Applicant</td>
                                    <td>Passport or government-issued ID (PDF or image format)</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="file" class="form-control form-control-sm" id="idProof"
                                                name="id_proof" required>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="bi bi-file-earmark-bar-graph text-success"></i></td>
                                    <td>Proof of Address</td>
                                    <td>Utility bill or bank statement less than 3 months old</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="file" class="form-control form-control-sm" id="addressProof"
                                                name="address_proof" required>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="bi bi-file-earmark-text text-primary"></i></td>
                                    <td>Business Plan</td>
                                    <td>Brief business plan outlining activities and objectives</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="file" class="form-control form-control-sm" id="businessPlan"
                                                name="business_plan" required>
                                        </div>
                                    </td>
                                </tr>
                                <tr id="shareholderAgreementDoc" style="display: none;">
                                    <td><i class="bi bi-file-earmark-person text-info"></i></td>
                                    <td>Shareholder Agreement</td>
                                    <td>Agreement between shareholders (required for multiple shareholders)</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="file" class="form-control form-control-sm"
                                                id="shareholderAgreement" name="shareholder_agreement">
                                        </div>
                                    </td>
                                </tr>
                                <tr id="bankReferenceDoc" style="display: none;">
                                    <td><i class="bi bi-bank text-warning"></i></td>
                                    <td>Bank Reference Letter</td>
                                    <td>Bank reference letter (required for Growth and Global FZC)</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="file" class="form-control form-control-sm" id="bankReference"
                                                name="bank_reference">
                                        </div>
                                    </td>
                                </tr>
                                <tr id="cooperativeCharterDoc" style="display: none;">
                                    <td><i class="bi bi-people text-success"></i></td>
                                    <td>Cooperative Charter</td>
                                    <td>Cooperative governance charter (required for Cooperative FZC)</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="file" class="form-control form-control-sm"
                                                id="cooperativeCharter" name="cooperative_charter">
                                        </div>
                                    </td>
                                </tr>
                                <tr id="digitalAssetProposalDoc" style="display: none;">
                                    <td><i class="bi bi-currency-bitcoin text-primary"></i></td>
                                    <td>Digital Asset Issuance Proposal</td>
                                    <td>Proposal for digital asset issuance (if applicable)</td>
                                    <td><span class="badge bg-secondary">Optional</span></td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="file" class="form-control form-control-sm"
                                                id="digitalAssetProposal" name="digital_asset_proposal">
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Contracts Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-file-earmark-text me-2"></i>Required Contracts</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">The following contracts must be signed:</p>

                        <table class="table table-striped table-hover align-middle">
                            <thead>
                                <tr>
                                    <th style="width: 5%"><i class="bi bi-file-earmark-text"></i></th>
                                    <th style="width: 20%">Contract</th>
                                    <th style="width: 45%">Description</th>
                                    <th style="width: 15%">Status</th>
                                    <th style="width: 15%">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Common contracts for all company types -->
                                <tr>
                                    <td><i class="bi bi-file-earmark-ruled text-danger"></i></td>
                                    <td>Articles of Incorporation</td>
                                    <td>Standard articles of incorporation for your company type</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewContract('articles')">View</button>
                                            <div class="form-check ms-1">
                                                <input class="form-check-input" type="checkbox"
                                                    id="contract-incorporation" name="contracts[]" value="incorporation"
                                                    required>
                                                <label class="form-check-label"
                                                    for="contract-incorporation">Sign</label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="bi bi-file-earmark-check text-primary"></i></td>
                                    <td>Terms & Conditions</td>
                                    <td>Standard terms and conditions for operating in the Digital Freezone</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewContract('terms')">View</button>
                                            <div class="form-check ms-1">
                                                <input class="form-check-input" type="checkbox" id="contract-terms"
                                                    name="contracts[]" value="terms" required>
                                                <label class="form-check-label" for="contract-terms">Sign</label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="bi bi-file-earmark-lock text-success"></i></td>
                                    <td>Compliance Agreement</td>
                                    <td>Agreement to comply with all Digital Freezone regulations</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewContract('compliance')">View</button>
                                            <div class="form-check ms-1">
                                                <input class="form-check-input" type="checkbox" id="contract-compliance"
                                                    name="contracts[]" value="compliance" required>
                                                <label class="form-check-label" for="contract-compliance">Sign</label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Type-specific contracts -->
                                <tr id="contract-item-shareholder" style="display: none;">
                                    <td><i class="bi bi-file-earmark-person text-info"></i></td>
                                    <td>Shareholder Rights Agreement</td>
                                    <td>Defines the rights and responsibilities of all shareholders</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewContract('shareholder')">View</button>
                                            <div class="form-check ms-1">
                                                <input class="form-check-input" type="checkbox"
                                                    id="contract-shareholder" name="contracts[]" value="shareholder">
                                                <label class="form-check-label" for="contract-shareholder">Sign</label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr id="contract-item-digital-assets" style="display: none;">
                                    <td><i class="bi bi-currency-bitcoin text-warning"></i></td>
                                    <td>Digital Asset Issuance Agreement</td>
                                    <td>Terms for issuing digital assets through your company</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewContract('digital-assets')">View</button>
                                            <div class="form-check ms-1">
                                                <input class="form-check-input" type="checkbox"
                                                    id="contract-digital-assets" name="contracts[]"
                                                    value="digital-assets">
                                                <label class="form-check-label"
                                                    for="contract-digital-assets">Sign</label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr id="contract-item-physical-assets" style="display: none;">
                                    <td><i class="bi bi-building text-secondary"></i></td>
                                    <td>Physical Asset Holding Agreement</td>
                                    <td>Terms for holding physical assets through your company</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewContract('physical-assets')">View</button>
                                            <div class="form-check ms-1">
                                                <input class="form-check-input" type="checkbox"
                                                    id="contract-physical-assets" name="contracts[]"
                                                    value="physical-assets">
                                                <label class="form-check-label"
                                                    for="contract-physical-assets">Sign</label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr id="contract-item-cooperative" style="display: none;">
                                    <td><i class="bi bi-people text-success"></i></td>
                                    <td>Cooperative Governance Agreement</td>
                                    <td>Governance structure for cooperative organizations</td>
                                    <td><span class="badge bg-danger">Required</span></td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewContract('cooperative')">View</button>
                                            <div class="form-check ms-1">
                                                <input class="form-check-input" type="checkbox"
                                                    id="contract-cooperative" name="contracts[]" value="cooperative">
                                                <label class="form-check-label" for="contract-cooperative">Sign</label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- Final agreement -->
                        <div class="mt-4 pt-3 border-top">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="contract-agreement"
                                    name="contract_agreement" required>
                                <label class="form-check-label" for="contract-agreement">
                                    <strong>I have read and agree to all the required contracts</strong>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(4)"><i
                            class="bi bi-arrow-left"></i> Previous</button>
                    <button type="submit" class="btn btn-success">Submit Registration</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Validation Error Modal -->
<div class="modal fade" id="validationModal" tabindex="-1" aria-labelledby="validationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="validationModalLabel">
                    <i class="bi bi-exclamation-triangle me-2"></i>Required Fields Missing
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <i class="bi bi-info-circle text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <p class="mb-2"><strong>Please complete all required fields before proceeding.</strong></p>
                        <p class="mb-3">The following fields are required and must be filled out:</p>
                        <ul id="missingFieldsList" class="list-unstyled mb-0">
                            <!-- Missing fields will be populated here -->
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="bi bi-pencil me-1"></i>Continue Editing
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .step-indicator {
        text-align: center;
        position: relative;
        opacity: 0.5;
    }

    .step-indicator.active {
        opacity: 1;
        font-weight: bold;
    }

    .step-indicator::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -50%;
        width: 100%;
        height: 2px;
        background-color: #dee2e6;
        z-index: -1;
    }

    .step-indicator:last-child::after {
        display: none;
    }
</style>
<script>
    // Multi-step form navigation
    function nextStep(currentStep) {
        // Validate current step
        if (!validateStep(currentStep)) {
            return false;
        }

        // Hide current step
        document.getElementById(`step-${currentStep}`).style.display = 'none';

        // Show next step
        document.getElementById(`step-${currentStep + 1}`).style.display = 'block';

        // Update progress bar
        updateProgressBar(currentStep + 1);

        // Update step indicators
        updateStepIndicators(currentStep + 1);

        // If moving to step 4 (documents), update required documents
        if (currentStep + 1 === 4) {
            updateRequiredDocuments();
        }

        // Scroll to top of form
        document.getElementById('companyRegistrationForm').scrollIntoView();

        return true;
    }

    function prevStep(currentStep) {
        // Hide current step
        document.getElementById(`step-${currentStep}`).style.display = 'none';

        // Show previous step
        document.getElementById(`step-${currentStep - 1}`).style.display = 'block';

        // Update progress bar
        updateProgressBar(currentStep - 1);

        // Update step indicators
        updateStepIndicators(currentStep - 1);

        // Scroll to top of form
        document.getElementById('companyRegistrationForm').scrollIntoView();

        return true;
    }

    function updateProgressBar(step) {
        const progressBar = document.querySelector('.progress-bar');
        const percentage = step * 25; // 4 steps, each 25%
        progressBar.style.width = `${percentage}%`;
        progressBar.setAttribute('aria-valuenow', percentage);
        progressBar.textContent = `Step ${step} of 4`;
    }

    function updateStepIndicators(activeStep) {
        // Reset all indicators
        document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const step = index + 1;
            indicator.classList.remove('active');
            const badge = indicator.querySelector('.badge');
            badge.classList.remove('bg-success');
            badge.classList.add('bg-secondary');
        });

        // Activate current step indicator
        const activeIndicator = document.getElementById(`step-indicator-${activeStep}`);
        activeIndicator.classList.add('active');
        const activeBadge = activeIndicator.querySelector('.badge');
        activeBadge.classList.remove('bg-secondary');
        activeBadge.classList.add('bg-success');

        // Mark completed steps
        for (let i = 1; i < activeStep; i++) {
            const completedIndicator = document.getElementById(`step-indicator-${i}`);
            const completedBadge = completedIndicator.querySelector('.badge');
            completedBadge.classList.remove('bg-secondary');
            completedBadge.classList.add('bg-success');
        }
    }

    function validateStep(step) {
        let valid = true;

        // Get all required fields in current step
        const currentStepElement = document.getElementById(`step-${step}`);
        const requiredFields = currentStepElement.querySelectorAll('[required]');

        // Check if all required fields are filled
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                valid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!valid) {
            showValidationModal();
        }

        return valid;
    }

    // Function to show validation modal with missing fields
    function showValidationModal() {
        const currentStepElement = document.getElementById(`step-${currentStep}`);
        const requiredFields = currentStepElement.querySelectorAll('[required]');
        const missingFieldsList = document.getElementById('missingFieldsList');

        // Clear previous list
        missingFieldsList.innerHTML = '';

        // Find missing fields and add to list
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                const li = document.createElement('li');
                li.className = 'mb-1';

                // Get field label or use placeholder/name as fallback
                let fieldLabel = '';
                const label = document.querySelector(`label[for="${field.id}"]`);
                if (label) {
                    fieldLabel = label.textContent.replace('*', '').trim();
                } else if (field.placeholder) {
                    fieldLabel = field.placeholder;
                } else {
                    fieldLabel = field.name || field.id;
                }

                li.innerHTML = `<i class="bi bi-arrow-right text-warning me-2"></i>${fieldLabel}`;
                missingFieldsList.appendChild(li);
            }
        });

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('validationModal'));
        modal.show();

        // Focus on first invalid field when modal is closed
        document.getElementById('validationModal').addEventListener('hidden.bs.modal', function () {
            const firstInvalid = currentStepElement.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, { once: true });
    }

    // Company type handling
    function updateCompanyTypeInfo() {
        const type = document.getElementById('companyType').value;
        const help = document.getElementById('companyTypeHelp');
        const shareholderHelp = document.getElementById('shareholderHelp');
        let maxShareholders = 0;
        let setupFee = 0;
        let monthlyFee = 0;
        let availabilityNote = '';

        switch (type) {
            case 'Single FZC':
                help.innerHTML = '1 shareholder. Cannot issue digital assets. Can hold external shares, connect to bank, participate in ecosystem.';
                shareholderHelp.innerHTML = 'Enter the sole shareholder. Only 1 allowed.';
                maxShareholders = 1;
                setupFee = 20;
                monthlyFee = 20;
                availabilityNote = 'Available immediately upon platform launch';
                break;
            case 'Startup FZC':
                help.innerHTML = 'Up to 5 shareholders. Can issue digital assets, hold external shares, connect to bank.';
                shareholderHelp.innerHTML = 'Add up to 5 shareholders.';
                maxShareholders = 5;
                setupFee = 50;
                monthlyFee = 50;
                availabilityNote = 'Available immediately upon platform launch';
                break;
            case 'Growth FZC':
                help.innerHTML = 'Up to 20 shareholders. Can hold physical assets. All Startup FZC features included.';
                shareholderHelp.innerHTML = 'Add up to 20 shareholders.';
                maxShareholders = 20;
                setupFee = 1000;
                monthlyFee = 100;
                availabilityNote = 'Available immediately upon platform launch';
                break;
            case 'Global FZC':
                help.innerHTML = 'Unlimited shareholders. All Growth FZC features included. Suitable for large/global operations. Additional per-shareholder fee applies.';
                shareholderHelp.innerHTML = 'Add all shareholders as needed. Additional monthly fee per shareholder ($2-20 depending on total count).';
                maxShareholders = 999; // Effectively unlimited
                setupFee = 2000;
                monthlyFee = 200;
                availabilityNote = 'Available in H2 2025';
                break;
            case 'Cooperative FZC':
                help.innerHTML = 'Unlimited members. Democratic governance, collective decision-making, equitable distribution. Additional per-member fee applies.';
                shareholderHelp.innerHTML = 'Add all members as needed. Additional monthly fee per member ($0.20-2 depending on total count).';
                maxShareholders = 999; // Effectively unlimited
                setupFee = 2000;
                monthlyFee = 200;
                availabilityNote = 'Available in H2 2025';
                break;
            default:
                help.innerHTML = 'Select the company type to see capabilities and requirements.';
                shareholderHelp.innerHTML = 'Add shareholders or members.';
                maxShareholders = 5; // Default
                setupFee = 20;
                monthlyFee = 20;
        }

        // Add availability note to help text if provided
        if (availabilityNote) {
            help.innerHTML += `<br><small class="text-muted">${availabilityNote}</small>`;
        }

        // Update pricing information
        updatePricingInfo(setupFee, monthlyFee);

        // Update add button visibility based on max shareholders
        updateAddButtonVisibility(maxShareholders);

        // Update capabilities table based on company type
        updateCapabilitiesTable(type);

        // If we're on the documents step, update required documents and contracts
        if (document.getElementById('step-4').style.display !== 'none') {
            updateRequiredDocuments();
            updateRequiredContracts(type);
        }
    }

    // Update pricing information based on company type
    function updatePricingInfo(setupFee, monthlyFee) {
        const twinFee = 2; // $2/month ZDFZ Twin fee
        const totalMonthlyFee = monthlyFee + twinFee;
        const yearlyTotal = setupFee + (totalMonthlyFee * 12);
        const yearlyDiscounted = setupFee + (totalMonthlyFee * 12 * 0.8); // 20% discount
        const twoYearDiscounted = setupFee + (totalMonthlyFee * 24 * 0.6); // 40% discount

        // Update pricing display
        document.getElementById('setupFee').textContent = `$${setupFee}`;
        document.getElementById('monthlyFee').textContent = `$${monthlyFee}`;
        document.getElementById('totalFirstYear').textContent = `$${yearlyTotal.toFixed(2)}`;
        document.getElementById('discountedYearly').textContent = `$${yearlyDiscounted.toFixed(2)}`;
    }

    // Update capabilities table with green ticks and red crosses
    function updateCapabilitiesTable(companyType) {
        // Define capabilities for each company type
        const capabilities = {
            'Single FZC': {
                'cap-digital-assets': true,     // Hold Digital Assets
                'cap-ecosystem': true,          // Participate in Ecosystem
                'cap-ai-dispute': true,         // AI Dispute Resolution
                'cap-digital-signing': true,    // Digital Signing
                'cap-issue-assets': false,      // Issue Digital Assets
                'cap-external-shares': true,    // External Shares
                'cap-bank-account': true,       // Bank Account
                'cap-physical-assets': false,   // Physical Assets
                'cap-democratic': false,        // Democratic Governance
                'cap-collective': false         // Collective Decision-Making
            },
            'Startup FZC': {
                'cap-digital-assets': true,     // Hold Digital Assets
                'cap-ecosystem': true,          // Participate in Ecosystem
                'cap-ai-dispute': true,         // AI Dispute Resolution
                'cap-digital-signing': true,    // Digital Signing
                'cap-issue-assets': true,       // Issue Digital Assets
                'cap-external-shares': true,    // External Shares
                'cap-bank-account': true,       // Bank Account
                'cap-physical-assets': false,   // Physical Assets
                'cap-democratic': false,        // Democratic Governance
                'cap-collective': false         // Collective Decision-Making
            },
            'Growth FZC': {
                'cap-digital-assets': true,     // Hold Digital Assets
                'cap-ecosystem': true,          // Participate in Ecosystem
                'cap-ai-dispute': true,         // AI Dispute Resolution
                'cap-digital-signing': true,    // Digital Signing
                'cap-issue-assets': true,       // Issue Digital Assets
                'cap-external-shares': true,    // External Shares
                'cap-bank-account': true,       // Bank Account
                'cap-physical-assets': true,    // Physical Assets
                'cap-democratic': false,        // Democratic Governance
                'cap-collective': false         // Collective Decision-Making
            },
            'Global FZC': {
                'cap-digital-assets': true,     // Hold Digital Assets
                'cap-ecosystem': true,          // Participate in Ecosystem
                'cap-ai-dispute': true,         // AI Dispute Resolution
                'cap-digital-signing': true,    // Digital Signing
                'cap-issue-assets': true,       // Issue Digital Assets
                'cap-external-shares': true,    // External Shares
                'cap-bank-account': true,       // Bank Account
                'cap-physical-assets': true,    // Physical Assets
                'cap-democratic': false,        // Democratic Governance
                'cap-collective': false         // Collective Decision-Making
            },
            'Cooperative FZC': {
                'cap-digital-assets': true,     // Hold Digital Assets
                'cap-ecosystem': true,          // Participate in Ecosystem
                'cap-ai-dispute': true,         // AI Dispute Resolution
                'cap-digital-signing': true,    // Digital Signing
                'cap-issue-assets': true,       // Issue Digital Assets
                'cap-external-shares': true,    // External Shares
                'cap-bank-account': true,       // Bank Account
                'cap-physical-assets': true,    // Physical Assets
                'cap-democratic': true,         // Democratic Governance
                'cap-collective': true          // Collective Decision-Making
            }
        };

        // Get capabilities for selected company type
        const typeCapabilities = capabilities[companyType];

        // Update each capability row in the table
        for (const [capId, isSupported] of Object.entries(typeCapabilities)) {
            const capRow = document.getElementById(capId);
            if (capRow) {
                const statusCell = capRow.querySelector('td:last-child');
                if (isSupported) {
                    statusCell.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
                    capRow.classList.remove('table-danger');
                    capRow.classList.add('table-success');
                } else {
                    statusCell.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i>';
                    capRow.classList.remove('table-success');
                    capRow.classList.add('table-danger');
                }
            }
        }
    }

    // Shareholder management
    function updateAddButtonVisibility(maxShareholders) {
        const container = document.getElementById('shareholdersContainer');
        const inputs = container.querySelectorAll('.shareholder-input');
        const addButtons = container.querySelectorAll('.add-shareholder');

        // Hide all add buttons first
        addButtons.forEach(btn => btn.style.display = 'none');

        // Show add button on the last input if we haven't reached the max
        if (inputs.length < maxShareholders) {
            const lastAddButton = inputs[inputs.length - 1].querySelector('.add-shareholder');
            if (lastAddButton) {
                lastAddButton.style.display = 'block';
            }
        }

        // Show/hide remove buttons based on number of inputs
        const removeButtons = container.querySelectorAll('.remove-shareholder');
        removeButtons.forEach(btn => {
            btn.style.display = inputs.length > 1 ? 'block' : 'none';
        });
    }

    function addShareholderField() {
        const container = document.getElementById('shareholdersContainer');
        const inputs = container.querySelectorAll('.shareholder-input');
        const type = document.getElementById('companyType').value;
        let maxShareholders = 999;

        // Determine max shareholders based on company type
        if (type === 'Single FZC') maxShareholders = 1;
        else if (type === 'Startup FZC') maxShareholders = 5;
        else if (type === 'Growth FZC') maxShareholders = 20;

        // Don't add more if we've reached the max
        if (inputs.length >= maxShareholders) {
            alert(`Maximum of ${maxShareholders} shareholders allowed for ${type}`);
            return;
        }

        // Create new input group
        const newInput = document.createElement('div');
        newInput.className = 'input-group mb-2 shareholder-input';
        newInput.innerHTML = `
        <input type="text" class="form-control shareholder-name" name="shareholder[]" placeholder="Shareholder name" required>
        <span class="input-group-text">%</span>
        <input type="number" class="form-control shareholder-percentage" name="percentage[]" min="0" max="100" step="0.01" value="0" ${isEqualDistribution() ? 'readonly' : ''}>
        <button type="button" class="btn btn-outline-success add-shareholder" title="Add another shareholder">
            <i class="bi bi-plus"></i>
        </button>
        <button type="button" class="btn btn-outline-danger remove-shareholder" title="Remove this shareholder">
            <i class="bi bi-dash"></i>
        </button>
    `;

        // Add event listeners to new buttons
        const addBtn = newInput.querySelector('.add-shareholder');
        addBtn.addEventListener('click', addShareholderField);

        const removeBtn = newInput.querySelector('.remove-shareholder');
        removeBtn.addEventListener('click', function () {
            container.removeChild(newInput);
            updateAddButtonVisibility(maxShareholders);
            updateShareholderPercentages();
            updateShareholdersHiddenField();
        });

        // Add event listener for percentage input changes
        const percentageInput = newInput.querySelector('.shareholder-percentage');
        percentageInput.addEventListener('input', validateTotalPercentage);

        // Add to container
        container.appendChild(newInput);

        // Update buttons visibility
        updateAddButtonVisibility(maxShareholders);

        // Update percentages for all shareholders
        updateShareholderPercentages();
    }

    // Check if equal distribution is selected
    function isEqualDistribution() {
        return document.getElementById('shareholderStructure').value === 'equal';
    }

    // Update shareholder percentages based on distribution type
    function updateShareholderPercentages() {
        const container = document.getElementById('shareholdersContainer');
        const inputs = container.querySelectorAll('.shareholder-input');
        const percentageInputs = container.querySelectorAll('.shareholder-percentage');

        // Set readonly attribute based on distribution type
        const isEqual = isEqualDistribution();
        percentageInputs.forEach(input => {
            input.readOnly = isEqual;
            if (isEqual) {
                input.classList.add('bg-light');
            } else {
                input.classList.remove('bg-light');
            }
        });

        // If equal distribution, calculate and set equal percentages
        if (isEqual && inputs.length > 0) {
            const equalPercentage = (100 / inputs.length).toFixed(2);
            percentageInputs.forEach(input => {
                input.value = equalPercentage;
            });

            // Hide percentage warning
            document.getElementById('percentageWarning').style.display = 'none';
        } else if (!isEqual) {
            // Validate total percentage
            validateTotalPercentage();
        }

        // Update hidden field with percentages
        updateShareholdersHiddenField();
    }

    // Validate that custom percentages add up to 100%
    function validateTotalPercentage() {
        if (isEqualDistribution()) return true;

        const container = document.getElementById('shareholdersContainer');
        const percentageInputs = container.querySelectorAll('.shareholder-percentage');
        const warningElement = document.getElementById('percentageWarning');

        let total = 0;
        percentageInputs.forEach(input => {
            total += parseFloat(input.value || 0);
        });

        // Round to 2 decimal places to avoid floating point issues
        total = Math.round(total * 100) / 100;

        if (total !== 100) {
            warningElement.style.display = 'block';
            warningElement.textContent = `Total percentage is ${total}%. It must equal 100%.`;
            return false;
        } else {
            warningElement.style.display = 'none';
            return true;
        }
    }

    // Update hidden fields with shareholder names and percentages
    function updateShareholdersHiddenField() {
        const container = document.getElementById('shareholdersContainer');
        const nameInputs = container.querySelectorAll('.shareholder-name');
        const percentageInputs = container.querySelectorAll('.shareholder-percentage');
        const namesHiddenField = document.getElementById('shareholders');
        const percentagesHiddenField = document.getElementById('shareholderPercentages');

        // Collect all shareholder names and percentages
        const shareholderNames = [];
        const shareholderPercentages = [];

        for (let i = 0; i < nameInputs.length; i++) {
            const name = nameInputs[i].value.trim();
            if (name) {
                shareholderNames.push(name);
                shareholderPercentages.push(percentageInputs[i].value);
            }
        }

        // Update hidden fields
        namesHiddenField.value = shareholderNames.join('\n');
        percentagesHiddenField.value = shareholderPercentages.join('\n');
    }

    // Document requirements
    function updateRequiredDocuments() {
        const type = document.getElementById('companyType').value;
        const shareholderCount = document.querySelectorAll('.shareholder-input').length;

        // Show/hide document rows based on company type
        const shareholderAgreementDoc = document.getElementById('shareholderAgreementDoc');
        const bankReferenceDoc = document.getElementById('bankReferenceDoc');
        const cooperativeCharterDoc = document.getElementById('cooperativeCharterDoc');
        const digitalAssetProposalDoc = document.getElementById('digitalAssetProposalDoc');

        // Shareholder agreement (required for multiple shareholders)
        if (shareholderCount > 1) {
            shareholderAgreementDoc.style.display = 'table-row';
            shareholderAgreementDoc.querySelector('input').required = true;
            // Update status badge
            const badge = shareholderAgreementDoc.querySelector('.badge');
            if (badge) badge.className = 'badge bg-danger';
            if (badge) badge.textContent = 'Required';
        } else {
            shareholderAgreementDoc.style.display = 'none';
            shareholderAgreementDoc.querySelector('input').required = false;
        }

        // Bank reference (required for Growth and Global FZC)
        if (type === 'Growth FZC' || type === 'Global FZC') {
            bankReferenceDoc.style.display = 'table-row';
            bankReferenceDoc.querySelector('input').required = true;
            // Update status badge
            const badge = bankReferenceDoc.querySelector('.badge');
            if (badge) badge.className = 'badge bg-danger';
            if (badge) badge.textContent = 'Required';
        } else {
            bankReferenceDoc.style.display = 'none';
            bankReferenceDoc.querySelector('input').required = false;
        }

        // Cooperative charter (required for Cooperative FZC)
        if (type === 'Cooperative FZC') {
            cooperativeCharterDoc.style.display = 'table-row';
            cooperativeCharterDoc.querySelector('input').required = true;
            // Update status badge
            const badge = cooperativeCharterDoc.querySelector('.badge');
            if (badge) badge.className = 'badge bg-danger';
            if (badge) badge.textContent = 'Required';
        } else {
            cooperativeCharterDoc.style.display = 'none';
            cooperativeCharterDoc.querySelector('input').required = false;
        }

        // Digital asset proposal (required for types that can issue digital assets)
        if (type !== 'Single FZC') {
            digitalAssetProposalDoc.style.display = 'table-row';
            // Not required by default, only if they plan to issue digital assets
            // Update status badge
            const badge = digitalAssetProposalDoc.querySelector('.badge');
            if (badge) badge.className = 'badge bg-secondary';
            if (badge) badge.textContent = 'Optional';
        } else {
            digitalAssetProposalDoc.style.display = 'none';
        }
    }

    // Update required contracts based on company type
    function updateRequiredContracts(type) {
        const shareholderCount = document.querySelectorAll('.shareholder-input').length;

        // Contract elements
        const shareholderContract = document.getElementById('contract-item-shareholder');
        const digitalAssetsContract = document.getElementById('contract-item-digital-assets');
        const physicalAssetsContract = document.getElementById('contract-item-physical-assets');
        const cooperativeContract = document.getElementById('contract-item-cooperative');

        // Shareholder Rights Agreement (required for multiple shareholders)
        if (shareholderCount > 1) {
            shareholderContract.style.display = 'table-row';
            shareholderContract.querySelector('input').required = true;
            // Update status badge
            const badge = shareholderContract.querySelector('.badge');
            if (badge) badge.className = 'badge bg-danger';
            if (badge) badge.textContent = 'Required';
        } else {
            shareholderContract.style.display = 'none';
            shareholderContract.querySelector('input').required = false;
        }

        // Digital Asset Issuance Agreement (required for Startup FZC and above)
        if (type !== 'Single FZC') {
            digitalAssetsContract.style.display = 'table-row';
            digitalAssetsContract.querySelector('input').required = type !== 'Cooperative FZC';
            // Update status badge
            const badge = digitalAssetsContract.querySelector('.badge');
            if (badge) {
                if (type !== 'Cooperative FZC') {
                    badge.className = 'badge bg-danger';
                    badge.textContent = 'Required';
                } else {
                    badge.className = 'badge bg-secondary';
                    badge.textContent = 'Optional';
                }
            }
        } else {
            digitalAssetsContract.style.display = 'none';
            digitalAssetsContract.querySelector('input').required = false;
        }

        // Physical Asset Holding Agreement (required for Growth FZC and above)
        if (type === 'Growth FZC' || type === 'Global FZC') {
            physicalAssetsContract.style.display = 'table-row';
            physicalAssetsContract.querySelector('input').required = true;
            // Update status badge
            const badge = physicalAssetsContract.querySelector('.badge');
            if (badge) badge.className = 'badge bg-danger';
            if (badge) badge.textContent = 'Required';
        } else {
            physicalAssetsContract.style.display = 'none';
            physicalAssetsContract.querySelector('input').required = false;
        }

        // Cooperative Governance Agreement (required for Cooperative FZC only)
        if (type === 'Cooperative FZC') {
            cooperativeContract.style.display = 'table-row';
            cooperativeContract.querySelector('input').required = true;
            // Update status badge
            const badge = cooperativeContract.querySelector('.badge');
            if (badge) badge.className = 'badge bg-danger';
            if (badge) badge.textContent = 'Required';
        } else {
            cooperativeContract.style.display = 'none';
            cooperativeContract.querySelector('input').required = false;
        }
    }

    function showTypeComparisonModal() {
        alert('See table below for company type comparison.');
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize company type info
        updateCompanyTypeInfo();

        // Initialize shareholder percentages
        updateShareholderPercentages();

        // Add event listener to company type dropdown
        document.getElementById('companyType').addEventListener('change', updateCompanyTypeInfo);

        // Add event listener to shareholder structure dropdown
        document.getElementById('shareholderStructure').addEventListener('change', updateShareholderPercentages);

        // Add event listener to first add button
        const firstAddBtn = document.querySelector('.add-shareholder');
        if (firstAddBtn) {
            firstAddBtn.addEventListener('click', addShareholderField);
        }

        // Add event listener to first percentage input
        const firstPercentageInput = document.querySelector('.shareholder-percentage');
        if (firstPercentageInput) {
            firstPercentageInput.addEventListener('input', validateTotalPercentage);
        }

        // Add event listener to form submission
        const form = document.querySelector('form');
        form.addEventListener('submit', function (e) {
            // Validate all steps before submission
            for (let i = 1; i <= 4; i++) {
                // Make the step visible temporarily for validation
                const step = document.getElementById(`step-${i}`);
                const wasHidden = step.style.display === 'none';
                if (wasHidden) step.style.display = 'block';

                // Validate
                if (!validateStep(i)) {
                    e.preventDefault();

                    // Show the step with errors
                    for (let j = 1; j <= 4; j++) {
                        document.getElementById(`step-${j}`).style.display = j === i ? 'block' : 'none';
                    }

                    // Update progress and indicators
                    updateProgressBar(i);
                    updateStepIndicators(i);

                    return false;
                }

                // Hide again if it was hidden
                if (wasHidden) step.style.display = 'none';
            }

            // Validate total percentage equals 100% for custom distribution
            if (!isEqualDistribution() && !validateTotalPercentage()) {
                e.preventDefault();

                // Show the shareholders step
                for (let j = 1; j <= 4; j++) {
                    document.getElementById(`step-${j}`).style.display = j === 3 ? 'block' : 'none';
                }

                // Update progress and indicators
                updateProgressBar(3);
                updateStepIndicators(3);

                return false;
            }

            // Update hidden fields before submission
            updateShareholdersHiddenField();
        });

        // Add event listener for input changes to update hidden field
        document.getElementById('shareholdersContainer').addEventListener('input', updateShareholdersHiddenField);
    });
</script>